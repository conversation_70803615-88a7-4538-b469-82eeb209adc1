<script>
    import Icon from '@iconify/svelte';
    import { Map, Tile<PERSON>ayer, <PERSON><PERSON>, Popup } from 'sveaflet';
    import { onMount } from "svelte";
    import { updateServizioCoordinates, selectVideoServizioById } from '@lib/supabase';
    import { getImmaginiServizio } from '@lib/supabase/storage';
    import { toastController } from 'ionic-svelte';
    import { Browser } from '@capacitor/browser';
    import VideoServizio from '@pages/VideoServizio.svelte';
    import { navController } from "ionic-svelte";
    import { t } from '@lib/i18n';
    import GalleryModal from './GalleryModal.svelte';
    import { parseMultiLanguageContent } from '@lib/i18n/content-parser';

    export let servizio;
    const giorni = ['lunedì', 'martedì', 'mercoledì', 'giovedì', 'venerdì', 'sabato', 'domenica'];

    let mapElement;
    let latitude = null;
    let longitude = null;
    let mapReady = false;
    let mapOptions = null;
    let isApproximateLocation = false;

    // Image slider variables
    let immagini = [];
    let currentImageIndex = 0;
    let sliderInterval;
    let isAutoSliding = false;
    let touchStartX = 0;
    let touchEndX = 0;
    let imagesLoaded = false;

    // Gallery modal
    let showGalleryModal = false;

    // Function to handle image slider navigation
    const nextImage = () => {
        if (immagini.length > 1) {
            currentImageIndex = (currentImageIndex + 1) % immagini.length;
        }
    };

    const prevImage = () => {
        if (immagini.length > 1) {
            currentImageIndex = (currentImageIndex - 1 + immagini.length) % immagini.length;
        }
    };

    // Touch handlers for swipe gestures
    const handleTouchStart = (e) => {
        touchStartX = e.touches[0].clientX;
    };

    const handleTouchEnd = (e) => {
        touchEndX = e.changedTouches[0].clientX;
        handleSwipe();
    };

    const handleSwipe = () => {
        const swipeThreshold = 50; // Minimum distance to be considered a swipe
        const swipeDistance = touchEndX - touchStartX;

        if (Math.abs(swipeDistance) < swipeThreshold) return;

        if (swipeDistance > 0) {
            // Swipe right -> previous image
            prevImage();
        } else {
            // Swipe left -> next image
            nextImage();
        }
    };

    const toggleAutoSlide = () => {
        isAutoSliding = !isAutoSliding;
        if (isAutoSliding) {
            sliderInterval = setInterval(nextImage, 3000);
        } else {
            clearInterval(sliderInterval);
        }
    };

    async function updateCoordinates() {
        if (!servizio.indirizzo) return;
        if (servizio.indirizzo.latitude && servizio.indirizzo.longitude) {
            latitude = parseFloat(servizio.indirizzo.latitude);
            longitude = parseFloat(servizio.indirizzo.longitude);
        } else {
            const address = getIndirizzo();
            const coords = await geocodeAddress(address);
            if (coords) {
                const { error } = await updateServizioCoordinates(servizio.id, coords.lat, coords.lon);
                if (!error) {
                    latitude = parseFloat(coords.lat);
                    longitude = parseFloat(coords.lon);
                }
            }
        }

        mapOptions = {
            center: [latitude, longitude],
            zoom: 14,
            invalidateSize: true,
            updateWhenIdle: false,
            updateWhenZooming: false,
            zoomControl: true
        };

        setTimeout(() => {
            mapReady = true;

            setTimeout(() => {
                if (mapElement?.map) {
                    mapElement.map.invalidateSize();
                    mapElement.map.setView([latitude, longitude], 14);
                }
            }, 250);
        }, 100);
    }

    async function tryGeocode(address) {
        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}`);
            const data = await response.json();

            if (data && data[0]) {
                return {
                    lat: parseFloat(data[0].lat),
                    lon: parseFloat(data[0].lon)
                };
            }
            return null;
        } catch (error) {
            console.error('Error in tryGeocode:', error);
            return null;
        }
    }

    async function geocodeAddress(address) {
        try {
            isApproximateLocation = false;

            const nameAndCity = `${servizio.nome}, ${servizio.indirizzo.città}, ${servizio.indirizzo.nazione}`;
            let coords = await tryGeocode(nameAndCity);
            if (coords) return coords;

            isApproximateLocation = true;

            const fullAddress = `${servizio.indirizzo.via}${servizio.indirizzo.numero_civico ? ', ' + servizio.indirizzo.numero_civico : ''}, ${servizio.indirizzo.città}, ${servizio.indirizzo.nazione}`;
            coords = await tryGeocode(fullAddress);
            if (coords) return coords;

            const cityOnly = `${servizio.indirizzo.città}, ${servizio.indirizzo.nazione}`;
            coords = await tryGeocode(cityOnly);
            if (coords) return coords;

            return null;
        } catch (error) {
            console.error('Geocoding error:', error);
            return null;
        }
    }

    onMount(async () => {
        try {
            await updateCoordinates();
            servizio.video = await selectVideoServizioById(servizio.id);

            // Always fetch fresh images to ensure we have the latest
            immagini = await getImmaginiServizio(servizio.nome);

            // If no images were found, try to use the anteprima
            if (immagini.length === 0 && servizio.anteprima) {
                immagini = [servizio.anteprima];
            }
        } catch (error) {
            console.error('Error in onMount:', error);
            // Fallback to anteprima in case of any error
            if (servizio.anteprima) {
                immagini = [servizio.anteprima];
            }
        } finally {
            // Force a UI update
            immagini = [...immagini];
            imagesLoaded = true;
        }

        // Clean up interval when component is destroyed
        return () => {
            if (sliderInterval) {
                clearInterval(sliderInterval);
            }
        };
    });

    const openLink = async (link) => {
        try {
            if (!link.startsWith('http://') && !link.startsWith('https://')) {
                link = 'https://' + link;
            }
            await Browser.open({ url: link });
        } catch (error) {
            const toast = await toastController.create({
                message: $t('error_opening_link'),
                duration: 2000,
                position: 'bottom',
                color: 'danger'
            });
            await toast.present();
        }
    }

    const getOrarioOrdinato = () => {
        if (!servizio.orari || servizio.orari.length == 0) return [];
        return giorni.map(giorno => ({
            giorno,
            orario: servizio.orari[0][giorno] || null
        }));
    }

    const getIndirizzo = () => {
        if (!servizio.indirizzo) return '';
        return `${servizio.indirizzo.via}${servizio.indirizzo.numero_civico ? ', ' + servizio.indirizzo.numero_civico : ''}${servizio.indirizzo.cap ? ', ' + servizio.indirizzo.cap : ''}${servizio.indirizzo.città ? ', ' + servizio.indirizzo.città : ''}`;
    }

    const getIndirizzoUrl = () => {
        return `https://www.google.com/maps/embed/v1/place?key=AIzaSyC0Lug7zgQ-JRtO3hXloqP8kI2DN80qHPo&q=${servizio.nome}+${getIndirizzo()}`;
    }

    const capitalizeFirstLetter = (string) => {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    function openInGoogleMaps() {
        const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(servizio.nome + ', ' + servizio.indirizzo.città)}`;
        window.open(url, '_blank');
    }
</script>

<ion-grid class="ion-no-padding main-grid">
    <!-- COPERTINA CON SLIDER -->
    <ion-row>
        <ion-col>
            <div
                class="image-slider-container"
                on:touchstart={handleTouchStart}
                on:touchend={handleTouchEnd}
            >
                {#if !imagesLoaded}
                    <div class="loading-container">
                        <ion-spinner name="circles"></ion-spinner>
                        <p>{$t('loading_content')}</p>
                    </div>
                {:else if immagini.length > 0}
                    <ion-img
                        src={immagini[currentImageIndex]}
                        alt={$t('service_image')}
                        on:ionError={(e) => {
                            e.target.src = 'placeholder.jpg';
                        }}
                    />
                {:else}
                    <ion-img
                        src={servizio.anteprima || 'placeholder.jpg'}
                        alt={$t('cover_image')}
                        on:ionError={(e) => {
                            e.target.src = 'placeholder.jpg';
                        }}
                    />
                {/if}

                <div id="premium-icon" class="absolute-top-right" class:ion-hide={!servizio.premium}>
                    <Icon icon="ion:star" color="var(--ion-color-warning)" width={25}/>
                </div>

                <!-- Navigation controls -->
                {#if imagesLoaded && immagini.length > 1}
                    <!-- Left button -->
                    <button class="slider-button prev" on:click={() => prevImage()}>
                        <Icon icon="ion:chevron-back" width={24} color="#000000" />
                    </button>

                    <!-- Right button -->
                    <button class="slider-button next" on:click={() => nextImage()}>
                        <Icon icon="ion:chevron-forward" width={24} color="#000000" />
                    </button>

                    <!-- Image counter -->
                    <div class="image-counter">
                        <small>{currentImageIndex + 1} / {immagini.length}</small>
                    </div>
                {/if}
            </div>
        </ion-col>
    </ion-row>

    <!-- Thumbnails row - only show if multiple images -->
    <!-- {#if imagesLoaded && immagini.length > 1}
        <ion-row>
            <ion-col>
                <div class="thumbnails-container">
                    {#each immagini as image, i}
                        <div
                            class="thumbnail"
                            class:active={i === currentImageIndex}
                            on:click={() => currentImageIndex = i}
                        >
                            <img
                                src={image}
                                alt={$t('thumbnail')}
                                on:error={(e) => {
                                    e.target.src = 'placeholder.jpg';
                                }}
                            />
                        </div>
                    {/each}
                </div>
            </ion-col>
        </ion-row>
    {/if} -->

    <!-- CONTENUTO -->
    <ion-row>
        <ion-col>
            <ion-grid class="ion-padding" >
                <!-- NOME -->
                <ion-row style:padding-bottom="10px">
                    <ion-col>
                        <ion-text class="h1">{servizio.nome}</ion-text>
                    </ion-col>
                </ion-row>

                <!-- CONTATTI -->
                <ion-row class="ion-padding-bottom" style:padding-bottom="16px">
                    <ion-col>
                        <ion-grid>
                            <!-- INDIRIZZO -->
                            {#if servizio.indirizzo}
                            <ion-row class="ion-align-items-center" style:padding-bottom="8px">
                                <ion-col size={1}>
                                    <Icon icon="ion:location-outline" width={25}/>
                                </ion-col>
                                <ion-col>
                                    <ion-text style:font-size="large">{getIndirizzo()}</ion-text>
                                </ion-col>
                            </ion-row>
                            {/if}

                            <!-- TELEFONO PRIMARIO -->
                             {#if servizio.telefono_primario}
                                <ion-row class="ion-align-items-center" style:padding-bottom="8px">
                                    <ion-col size={1}>
                                        <Icon icon="ion:call-outline" width={25}/>
                                    </ion-col>
                                    <ion-col>
                                        <ion-text style:font-size="large"><a href={'tel:' + (servizio.telefono_primario.startsWith('+') ? '' : '+39 ') + servizio.telefono_primario} style:color="var(--ion-color-primary-foreground)">{(servizio.telefono_primario.startsWith('+') ? '' : '+39 ')}{servizio.telefono_primario}</a></ion-text>
                                    </ion-col>
                                </ion-row>
                             {/if}

                             <!-- TELEFONO SECONDARIO -->
                            {#if servizio.telefono_secondario}
                                <ion-row class="ion-align-items-center" style:padding-bottom="8px">
                                    <ion-col size={1} style:margin-right="4px">
                                        <Icon icon="ion:call-outline" width={25}/>

                                    </ion-col>
                                    <ion-col>
                                        <ion-text style:font-size="large"><a href={'tel:+39 '+servizio.telefono_secondario} style:color="var(--ion-color-primary-foreground)" >+39 {servizio.telefono_secondario}</a></ion-text>
                                    </ion-col>
                                </ion-row>
                            {/if}

                            <!-- EMAIL -->
                            {#if servizio.email}
                                <ion-row class="ion-align-items-center" style:padding-bottom="8px">
                                    <ion-col size={1} style:margin-right="4px">
                                        <Icon icon="ion:mail-outline" width={25}/>

                                    </ion-col>
                                    <ion-col>
                                        <ion-text style:font-size="large"><a href={'mailto:'+servizio.email} style:color="var(--ion-color-primary-foreground)" >{servizio.email}</a></ion-text>
                                    </ion-col>
                                </ion-row>
                            {/if}

                            <!-- SITO WEB -->
                             {#if servizio.sito_web}
                                <ion-button expand="block" fill="outline" style:margin-top="20px" on:click={() => openLink(servizio.sito_web)}>
                                    <ion-text>{$t('visit_website')}</ion-text>
                                </ion-button>
                             {/if}

                            <!-- PRENOTAZIONI -->
                            {#if servizio.link_prenotazioni}
                                <ion-button expand="block" fill="outline" style:margin-top="20px" on:click={() => openLink(servizio.link_prenotazioni)}>
                                    <ion-text>{$t('click_to_book')}</ion-text>
                                </ion-button>
                            {/if}

                            {#if immagini.length > 1}
                                <ion-button expand="block" fill="outline" style:margin-top="20px" on:click={() => showGalleryModal = true}>
                                    <ion-text>{$t('view_gallery')}</ion-text>
                                </ion-button>
                            {/if}

                            <!-- VIDEO -->
                            {#if servizio.video?.length > 0}
                                <ion-button expand="block" fill="outline" style:margin-top="20px" on:click={() => navController.push(VideoServizio, {servizio})}>
                                    <ion-text>{$t('watch_related_videos')}</ion-text>
                                </ion-button>
                            {/if}

                        </ion-grid>
                    </ion-col>
                </ion-row>

                <!-- INDIRIZZO -->
                 {#if servizio.indirizzo}
                    <ion-row>
                        <ion-col size={12} style:padding-bottom="20px">
                            {#if latitude && longitude && mapReady && mapOptions}
                                <div
                                    class="map-container"
                                    on:click={openInGoogleMaps}
                                    role="button"
                                    tabindex="0"
                                >
                                    <Map
                                        bind:this={mapElement}
                                        options={mapOptions}
                                    >
                                        <TileLayer
                                            url={'https://tile.openstreetmap.org/{z}/{x}/{y}.png'}
                                            options={{
                                                maxZoom: 19,
                                                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                            }}
                                        />
                                        <Marker latLng={[latitude, longitude]}>
                                            <Popup options={{
                                                content: isApproximateLocation
                                                    ? `${servizio.nome} (${$t('approximate_location')})`
                                                    : servizio.nome
                                            }} />
                                        </Marker>
                                    </Map>
                                </div>
                            {:else}
                                <div class="map-loading">
                                    <ion-spinner name="crescent"></ion-spinner>
                                </div>
                            {/if}
                        </ion-col>
                    </ion-row>
                {/if}

                <!-- DESCRIZIONE -->
                {#if servizio.descrizione}
                    <ion-row class="ion-padding-bottom">
                        <ion-col>
                            <hr style="width: 40%; margin: 20px auto; border: none; border-top: 1.5px solid var(--ion-color-medium);">
                            <ion-text class="descrizione">{parseMultiLanguageContent(servizio.descrizione)}</ion-text>
                            <hr style="width: 40%; margin: 20px auto; border: none; border-top: 1.5px solid var(--ion-color-medium);">
                        </ion-col>
                    </ion-row>
                {/if}

                <!-- ORARI -->
                {#if servizio.orari && servizio.orari.length > 0}
                    <ion-row class="ion-padding-bottom">
                        <ion-col size={12} style:padding-bottom="10px">
                            <ion-text class="h2">{$t('hours')}</ion-text>
                        </ion-col>
                        {#if servizio.orari[0].h24}
                            <ion-col size={12}>
                                <ion-text style:font-size="large">{$t('open_24_7')}</ion-text>
                            </ion-col>
                        {:else}
                            <ion-col size={12}>
                                <ion-grid class="ion-no-padding">
                                    {#each getOrarioOrdinato() as {giorno, orario}}
                                        <ion-row>
                                            <ion-col size={3}>
                                                <ion-text>{capitalizeFirstLetter(giorno)}</ion-text>
                                            </ion-col>
                                            <ion-col>
                                                <ion-text style:font-size="large">{orario}</ion-text>
                                            </ion-col>
                                        </ion-row>
                                    {/each}
                                </ion-grid>
                            </ion-col>
                        {/if}
                    </ion-row>
                {/if}
            </ion-grid>
        </ion-col>
    </ion-row>


</ion-grid>

<!-- Gallery Modal -->
{#if showGalleryModal}
    <GalleryModal
        images={immagini}
        initialIndex={currentImageIndex}
        on:close={() => showGalleryModal = false}
    />
{/if}

<style>
    .absolute-top-right {
        position: absolute;
        top: 0;
        right: 0;
        margin: 10px;
        z-index: 10;
    }

    .image-slider-container {
        position: relative;
        width: 100%;
        height: 256px;
        overflow: hidden;
        background-color: #f0f0f0;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* Add a subtle animation for image transitions */
    @keyframes fadeIn {
        from { opacity: 0.5; }
        to { opacity: 1; }
    }

    ion-img {
        height: 256px;
        object-fit: cover;
        width: 100%;
        transition: opacity 0.3s ease-in-out;
        animation: fadeIn 0.3s;
    }

    .slider-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: rgba(255, 255, 255, 0.3);
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        color: #000;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        z-index: 10;
    }

    .slider-button.prev {
        left: 10px;
    }

    .slider-button.next {
        right: 10px;
    }

    .image-counter {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        padding: 5px 10px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: bold;
        z-index: 5;
    }

    .loading-container {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        width: 100%;
        background-color: #f5f5f5;
    }

    .loading-container p {
        margin-top: 10px;
        color: var(--ion-color-medium);
        font-size: 16px;
    }

    .map-container {
        width: 100%;
        height: 150px;
        border-radius: 25px;
        box-shadow: 8px 8px 20px gray;
        overflow: hidden;
        position: relative;
        z-index: 1;
        cursor: pointer;
        background-color: white;
    }

    .map-loading {
        width: 100%;
        height: 150px;
        border-radius: 25px;
        box-shadow: 2px 2px 5px gray;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f5f5f5;
    }

    :global(.leaflet-container) {
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: white !important;
    }

    :global(.leaflet-tile-container) {
        height: 100% !important;
        width: 100% !important;
    }

    :global(.leaflet-tile) {
        visibility: inherit !important;
    }

    ion-text.descrizione {
        font-size: large;
        text-align: justify;
        display: inline-block;
        width: 100%;
        hyphens: auto;
        -webkit-hyphens: auto;
        -ms-hyphens: auto;
    }

    /* Thumbnails styles */
    .thumbnails-container {
        display: flex;
        overflow-x: auto;
        gap: 8px;
        padding: 10px 5px;
        width: 100%;
        justify-content: center;
        min-height: 70px;
        max-height: 90px;
        scrollbar-width: thin;
        scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);
        background-color: #f5f5f5;
        border-radius: 0 0 8px 8px;
    }

    .thumbnails-container::-webkit-scrollbar {
        height: 4px;
    }

    .thumbnails-container::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1);
        border-radius: 2px;
    }

    .thumbnails-container::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 2px;
    }

    .thumbnail {
        width: 60px;
        height: 60px;
        border-radius: 4px;
        overflow: hidden;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s, transform 0.2s, border 0.2s;
        flex-shrink: 0;
        border: 2px solid transparent;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    }

    .thumbnail.active {
        opacity: 1;
        transform: scale(1.05);
        border: 2px solid var(--ion-color-primary, #3880ff);
    }

    .thumbnail img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    @media (max-width: 576px) {
        .thumbnails-container {
            min-height: 60px;
            max-height: 70px;
            padding: 5px;
        }

        .thumbnail {
            width: 50px;
            height: 50px;
        }
    }
</style>