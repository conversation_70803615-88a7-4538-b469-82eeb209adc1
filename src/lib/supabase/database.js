import { supabase } from './supabaseClient';

/* INSERT */
export const insertUtente = async (idDispositivo) => {
    try {
        const records = await supabase.from('utenti').insert({ id_dispositivo: idDispositivo });
        
        return records;
    } catch (error) {
        console.log(error.stack);
    }
}

export const insertRichiestaInserimentoAttivita = async (datiRichiesta) => {
    try {
        const records = await supabase.from('richieste_inserimento_attivita').insert({
            responsabile_nome: datiRichiesta.responsabile_nome,
            responsabile_cognome: datiRichiesta.responsabile_cognome,
            nome_azienda: datiRichiesta.nome_azienda,
            ragione_sociale: datiRichiesta.ragione_sociale,
            via: datiRichiesta.via,
            paese: datiRichiesta.paese,
            cap: datiRichiesta.cap,
            telefono: datiRichiesta.telefono,
            email: datiRichiesta.email,
            piva_cf: datiRichiesta.piva_cf,
            note: datiRichiesta.note,
            paese_visibilita: datiRichiesta.paese_visibilita,
            categoria: datiRichiesta.categoria,
            video_link: datiRichiesta.video_link,
            indirizzo_visibilita: datiRichiesta.indirizzo_visibilita,
            telefono_visibilita: datiRichiesta.telefono_visibilita,
            email_visibilita: datiRichiesta.email_visibilita,
            sito_web: datiRichiesta.sito_web,
            codice_intermediario: datiRichiesta.codice_intermediario,
            data_richiesta: new Date().toISOString()
        });
        
        return records;
    } catch (error) {
        console.log(error.stack);
        throw error;
    }
}

export const insertRichiestaContatto = async (datiRichiesta) => {
    try {
        const { data, error } = await supabase.from('richieste_contatto').insert({
            nome: datiRichiesta.nome,
            cognome: datiRichiesta.cognome,
            email: datiRichiesta.email,
            messaggio: datiRichiesta.messaggio,
            data_richiesta: new Date().toISOString()
        });
        
        if (error) {
            console.error('Supabase error:', error);
            throw error;
        }
        
        return data;
    } catch (error) {
        console.error('Full error:', error);
        throw error;
    }
}

/* SELECT */
export const selectConfigurazione = async (nomeConfigurazione) => {
    try {        
        const records = await supabase.from('configurazioni').select().eq('nome', nomeConfigurazione).single();
        
        return records.data.valore;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectComuni = async () => {
    try {        
        const records = await supabase.from('comuni').select().eq('visibile', true).order('sequenza_visualizzazione');
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectGiuntaComunaleByComuneId = async (idComune) => {
    try {        
        const records = await supabase.from('giunte_comunali').select().eq('comune_id', idComune).order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectVideoComuneById = async (idComune) => {
    try {
        const records = await supabase.from('video').select().eq('comune_id', idComune);
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategorieRange = async (startIndex, endIndex) => {
    try {
        const records = await supabase.from('categorie')
            .select()
            .range(startIndex, endIndex)
            .order('tipo')
            .order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategorieByQuery = async (query) => {
    try {
        const records = await supabase.from('categorie')
            .select()
            .ilike('nome', `%${query}%`)
            .order('tipo')
            .order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategorieByTipo = async (tipoCategoria, categorieDaEscludereIds, limit) => {
    try {
        let records;
        
        if(limit)
            if(categorieDaEscludereIds)
                records = await supabase.from('categorie').select().not('id', 'in', '('+ categorieDaEscludereIds +')').eq('tipo', tipoCategoria).order('sequenza_visualizzazione').limit(limit);
            else
                records = await supabase.from('categorie').select().eq('tipo', tipoCategoria).order('sequenza_visualizzazione').limit(limit);
        else if(categorieDaEscludereIds)
            records = await supabase.from('categorie').select().not('id', 'in', '('+ categorieDaEscludereIds +')').eq('tipo', tipoCategoria).order('sequenza_visualizzazione');
        else
            records = await supabase.from('categorie').select().eq('tipo', tipoCategoria).order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategorieInEvidenza = async () => {
    try {
        let records;
        
        records = await supabase.from('in_evidenza').select().order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategorieByInEvidenzaId = async (idInEvidenza) => {
    try {
        let records;
        
        records = await supabase.from('categorie').select().eq('in_evidenza_id', idInEvidenza).order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategoriePerIlTurista = async (limit) => {
    try {
        let records;

        if(limit)
            records = await supabase.from('categorie').select().eq('per_il_turista', true).order('sequenza_visualizzazione').limit(limit);
        else
            records = await supabase.from('categorie').select().eq('per_il_turista', true).order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectCategorieSalentoWorld = async () => {
    try {
        const records = await supabase.from('categorie').
            select()
            .or(`tipo.eq.${'Artisti'},tipo.eq.${'Nazione'}`)
            .order('sequenza_visualizzazione');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectListaCategorieArtisti = async () => {
    try {
        const records = await supabase
            .from('categorie_artisti')
            .select('*, artisti!inner(*)')
            .order('nome');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectArtisti = async () => {
    try {
        const records = await supabase.from('artisti').select();
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectServiziByNazione = async (nomeNazione) => {
    try {
        const records = await supabase.from('servizi')
            .select(`*,
                indirizzi (via, numero_civico, cap, città, provincia, nazione, latitude, longitude),
                orari(lunedì, martedì, mercoledì, giovedì, venerdì, sabato, domenica, h24)
            `)
            .order('nome');

            console.log(records.data);
            console.log(nomeNazione);
            records.data = records.data.filter((servizio) => servizio.indirizzi[0]?.nazione == nomeNazione);
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectServiziByQuery = async (idCategoria, query) => {
    try {
        let records;

        records = await supabase.from('servizi')
            .select(`*,
                indirizzi (via, numero_civico, cap, città, provincia, nazione, latitude, longitude),
                orari(lunedì, martedì, mercoledì, giovedì, venerdì, sabato, domenica, h24)
            `)
            .eq('categoria_id', idCategoria)
            .ilike('nome', `%${query}%`)
            .order('premium', { ascending: false, nullsFirst: false })
            .order('nome');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectServiziRangeByComune = async (idCategoria, nomeComune, startIndex, endIndex) => {
    try {
        let records;

        records = await supabase.from('servizi')
            .select(`*,
                indirizzi (via, numero_civico, cap, città, provincia, nazione,latitude, longitude),
                orari(lunedì, martedì, mercoledì, giovedì, venerdì, sabato, domenica, h24)
            `)
            .eq('categoria_id', idCategoria)
            .order('premium', { ascending: false, nullsFirst: false })
            .order('nome');

        if(nomeComune)
            records.data = records.data.filter((servizio) => servizio.indirizzi[0]?.città == nomeComune || servizio.comuni_aggiuntivi?.includes(nomeComune));

        records.data = records.data.slice(startIndex, endIndex);

        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectServiziRangeByDintorniDi = async (idCategoria, nomeComune, startIndex, endIndex) => {
    try {
        let records;

        records = await supabase.from('servizi')
            .select(`*,
                indirizzi (via, numero_civico, cap, città, provincia, nazione, latitude, longitude),
                orari(lunedì, martedì, mercoledì, giovedì, venerdì, sabato, domenica, h24)
            `)
            .eq('categoria_id', idCategoria)
            .like('dintorni_di', `%${nomeComune}%`)
            .range(startIndex, endIndex)
            .order('premium', { ascending: false, nullsFirst: false })
            .order('nome');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectServiziRangePerIlTurista = async (idCategoria, startIndex, endIndex) => {
    try {
        let records;

        records = await supabase.from('servizi')
            .select(`*,
                indirizzi (via, numero_civico, cap, città, provincia, nazione, latitude, longitude),
                orari(lunedì, martedì, mercoledì, giovedì, venerdì, sabato, domenica, h24)
            `)
            .eq('categoria_id', idCategoria)
            .eq('per_il_turista', true)
            .order('premium', { ascending: false, nullsFirst: false })
            .order('nome');
        
        records.data = records.data.slice(startIndex, endIndex);

        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectServiziInEvidenza = async (idInEvidenza) => {
    try {
        const records = await supabase.from('servizi')
            .select(`*,
                indirizzi (via, numero_civico, cap, città, provincia, nazione, latitude, longitude),
                orari(lunedì, martedì, mercoledì, giovedì, venerdì, sabato, domenica, h24)
            `)
            .eq('in_evidenza_id', idInEvidenza)
            .order('premium', { ascending: false, nullsFirst: false })
            .order('nome');
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectVideoServizioById = async (idServizio) => {
    try {
        const records = await supabase.from('video').select().eq('servizio_id', idServizio);
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectAllEventi = async () => {
    try {
        const currentDate = new Date().toISOString().split('T')[0];
        const currentTime = new Date().toLocaleTimeString();

        const records = await supabase.from('eventi')
            .select('*, comuni (nome)')
            .or(`data.gt.${currentDate},and(data.eq.${currentDate},ora_fine.gte.${currentTime})`)
            .order('data')
            .order('ora_inizio');
        
        for (const evento of records.data) {
            evento.comune = evento.comuni.nome;
            delete evento.comuni;

            if(evento.data == currentDate && evento.ora_inizio <= currentTime && evento.ora_fine >= currentTime)
                evento.in_corso = true;
        }
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectEventiByComuneId = async (idComune) => {
    try {
        const currentDate = new Date().toISOString().split('T')[0];
        const currentTime = new Date().toLocaleTimeString();

        const records = await supabase.from('eventi')
            .select()
            .eq('comune_id', idComune)
            .or(`data.gt.${currentDate},and(data.eq.${currentDate},ora_fine.gte.${currentTime})`)
            .order('data')
            .order('ora_inizio');
        
        for (const evento of records.data) {            
            if(evento.data == currentDate && evento.ora_inizio <= currentTime && evento.ora_fine >= currentTime)
                evento.in_corso = true;
        }
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectEventiInEvidenza = async (idInEvidenza) => {
    try {
        const currentDate = new Date().toISOString().split('T')[0];
        const currentTime = new Date().toLocaleTimeString();

        const records = await supabase.from('eventi')
            .select(`*, comuni (nome)`)
            .eq('in_evidenza_id', idInEvidenza)
            .or(`data.gt.${currentDate},and(data.eq.${currentDate},ora_fine.gte.${currentTime})`)
            .order('data')
            .order('ora_inizio');
    
        for (const evento of records.data) {            
            if(evento.data == currentDate && evento.ora_inizio <= currentTime && evento.ora_fine >= currentTime)
                evento.in_corso = true;
        }
        
        for (const evento of records.data) {
            evento.comune = evento.comuni.nome;
            delete evento.comuni;
        }
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectAnnunci = async (idComune) => {
    try {
        const currentDate = new Date().toISOString().split('T')[0];

        const records = await supabase.from('annunci')
            .select()
            .eq('comune_id', idComune)
            .lte('inizio_validità', currentDate)
            .gte('fine_validità', currentDate)
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectBachecaItemByTipo = async (tipo, comuneId) => {
    try {
        const records = await supabase.from('bacheche')
            .select()
            .eq('comune_id', comuneId)
            .eq('tipo', tipo)
            .order('data_pubblicazione', { ascending: false });
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}

/* UPDATE */
export const updateComuneCoordinates = async (idComune, latitude, longitude) => {
    try {
        const records = await supabase.from('comuni').update({ latitude, longitude }).eq('id', idComune);
        
        return records;
    } catch (error) {
        console.log(error.stack);
    }
}

export const updateServizioCoordinates = async (idServizio, latitude, longitude) => {
    try {        
        const records = await supabase.from('indirizzi').update({ latitude, longitude }).eq('servizio_id', idServizio);
        
        return records;
    } catch (error) {
        console.log(error.stack);
    }
}

export const selectVideoInEvidenza = async (idInEvidenza) => {
    try {
        const records = await supabase.from('video')
        .select()
        .eq('in_evidenza_id', idInEvidenza);
        
        return records.data;
    } catch (error) {
        console.log(error.stack);
    }
}
